package com.frt.generalgw.domain.param.operationadmin.auth;

import lombok.Getter;
import lombok.Setter;

/**
 * 账号登录参数
 * 接口名称：operation/web/login
 * 请求方式：POST
 */
@Getter
@Setter
public class OperationAdminLoginParam {
    /**
     * 二级域名
     */
    private String webAddress;
    
    /**
     * 租户Id
     */
    private String tenantId;
    
    /**
     * 登录方式 1-密码登录 2-验证码登录
     */
    private Integer type;
    
    /**
     * 账号
     */
    private String account;
    
    /**
     * 密码（md5加密）
     */
    private String password;
    
    /**
     * 验证码
     */
    private String code;
}