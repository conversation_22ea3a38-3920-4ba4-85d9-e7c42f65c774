package com.frt.generalgw.domain.result.operationadmin.auth;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 登录结果
 * 接口名称：operation/web/login
 * 请求方式：POST
 */
@Getter
@Setter
public class OperationAdminLoginResult {
    /**
     * token
     */
    private String token;
    
    /**
     * 菜单列表
     */
    private List<String> menuList;
    
    /**
     * 功能列表
     */
    private List<String> funcList;
    
    /**
     * 账号信息
     */
    private UserInfo userInfo;
    
    /**
     * 租户信息
     */
    private TenantInfo tenantInfo;
    
    /**
     * 协议列表
     */
    private List<ProtocolInfo> protocolList;

    @Getter
    @Setter
    public static class UserInfo {
        /**
         * 账号id
         */
        private String accountId;
        
        /**
         * 账号
         */
        private String account;
        
        /**
         * 是否是管理员,0不是.1是
         */
        private String isAdmin;
        
        /**
         * 姓名
         */
        private String name;
        
        /**
         * 手机号
         */
        private String phone;
    }

    @Getter
    @Setter
    public static class TenantInfo {
        /**
         * 租户id
         */
        private String tenantId;
        
        /**
         * 租户名称
         */
        private String tenantName;
        
        /**
         * 租户联系人电话
         */
        private String phone;
    }

    @Getter
    @Setter
    public static class ProtocolInfo {
        /**
         * 协议id
         */
        private String protocolId;
        
        /**
         * 协议名称
         */
        private String protocolName;
        
        /**
         * 协议类型
         */
        private String protocolType;
    }
}