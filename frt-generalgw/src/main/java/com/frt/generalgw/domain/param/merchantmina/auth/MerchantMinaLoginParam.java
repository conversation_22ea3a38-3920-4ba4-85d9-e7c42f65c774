package com.frt.generalgw.domain.param.merchantmina.auth;

import lombok.Getter;
import lombok.Setter;

/**
 * 账号登录参数
 * 接口名称：merchant/mina/login
 * 请求方式：POST
 */
@Getter
@Setter
public class MerchantMinaLoginParam {
    /**
     * 小程序appId
     */
    private String appId;
    
    /**
     * 租户Id
     */
    private String tenantId;
    
    /**
     * 登录方式 1-密码登录 2-验证码登录
     */
    private Integer type;
    
    /**
     * 账号
     */
    private String account;
    
    /**
     * 密码（md5加密）
     */
    private String password;
    
    /**
     * 验证码
     */
    private String code;
}