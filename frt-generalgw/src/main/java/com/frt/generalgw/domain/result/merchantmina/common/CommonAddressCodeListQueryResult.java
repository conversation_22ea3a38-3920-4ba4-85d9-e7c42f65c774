package com.frt.generalgw.domain.result.merchantmina.common;

import lombok.Data;

import java.util.List;

@Data
public class CommonAddressCodeListQueryResult {
    /**
     * 省
     */
    public String provinceName;
    /**
     * 省code
     */
    public String provinceCode;
    /**
     * 市
     */
    public String cityName;
    /**
     * 市code
     */
    public String cityCode;
    public List<CommonAddressCodeInfoQueryResult> list;


    @Data
    public static class CommonAddressCodeInfoQueryResult {
        /**
         * 地区code
         */
        public String code;
        /**
         * 地区name
         */
        public String name;
    }
}
