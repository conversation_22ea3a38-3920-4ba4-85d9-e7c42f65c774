/**
 * fshows.com
 * Copyright (C) 2013-2025 All Rights Reserved.
 */
package com.frt.generalgw.controller.merchantadmin;

import com.frt.generalgw.client.usercore.merchantadmin.MerchantAdminCommonClient;
import com.frt.generalgw.domain.param.merchantadmin.common.CommonAddressCodeListQueryParam;
import com.frt.generalgw.domain.param.merchantadmin.common.CommonUnityCategoryListQueryParam;
import com.frt.generalgw.domain.result.merchantadmin.common.CommonAddressCodeListQueryResult;
import com.frt.generalgw.domain.result.merchantadmin.common.CommonUnityCategoryListQueryResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 商家版后台/CommonController
 */
@RestController
@RequestMapping(value = "/merchantweb/common")
public class CommonController {

    @Autowired
    private MerchantAdminCommonClient commonClient;

    /**
     * 查询地址列表
     * @param param
     * @return
     */
    @PostMapping("/query/address-code-list")
    public CommonAddressCodeListQueryResult queryAddressCodeList(@RequestBody CommonAddressCodeListQueryParam param) {
        return commonClient.queryAddressCodeList(param);
    }

    /**
     * 查询类目列表
     *
     * @param param
     * @return
     */
    @PostMapping("/query/unity-category-list")
    public CommonUnityCategoryListQueryResult queryUnityCategoryList(@RequestBody CommonUnityCategoryListQueryParam param) {
        return commonClient.queryUnityCategoryList(param);
    }
}