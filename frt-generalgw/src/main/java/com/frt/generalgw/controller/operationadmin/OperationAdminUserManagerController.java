package com.frt.generalgw.controller.operationadmin;

import com.frt.generalgw.domain.param.common.PageParam;
import com.frt.generalgw.domain.param.operationadmin.usermanager.UserAddParam;
import com.frt.generalgw.domain.param.operationadmin.usermanager.UserDetailQueryParam;
import com.frt.generalgw.domain.param.operationadmin.usermanager.UserListQueryParam;
import com.frt.generalgw.domain.param.operationadmin.usermanager.UserModifyParam;
import com.frt.generalgw.domain.result.common.PageResult;
import com.frt.generalgw.domain.result.operationadmin.usermanager.UserDetailQueryResult;
import com.frt.generalgw.domain.result.operationadmin.usermanager.UserListQueryResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 运营后台/OperationAdminUserManagerController
 *
 * <AUTHOR>
 * @version UserManagerController.java, v 0.1 2025-08-27 15:00 tuyuwei
 */
@RestController
@RequestMapping("/operation/web/user")
public class OperationAdminUserManagerController {

    /**
     * 员工列表
     *
     * @param param 请求参数
     * @return 员工列表
     */
    @PostMapping("/user-list")
    public PageResult<UserListQueryResult> getUserList(@Validated @RequestBody PageParam<UserListQueryParam> param) {
        // TODO: 实现员工列表查询逻辑
        return new PageResult<>();
    }

    /**
     * 新增员工
     *
     * @param param 请求参数
     */
    @PostMapping("/user-add")
    public void addUser(@Validated @RequestBody UserAddParam param) {
        // TODO: 实现新增员工逻辑
    }

    /**
     * 修改员工
     *
     * @param param 请求参数
     */
    @PostMapping("/user-modify")
    public void modifyUser(@Validated @RequestBody UserModifyParam param) {
        // TODO: 实现修改员工逻辑
    }

    /**
     * 查询员工信息
     *
     * @param param 请求参数
     * @return 员工信息
     */
    @PostMapping("/user-detail")
    public UserDetailQueryResult getUserDetail(@Validated @RequestBody UserDetailQueryParam param) {
        // TODO: 实现查询员工信息逻辑
        return new UserDetailQueryResult();
    }
}
