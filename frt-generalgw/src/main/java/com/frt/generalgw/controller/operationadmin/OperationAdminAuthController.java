/**
 * <AUTHOR>
 * @date 2025/8/27 14:45
 * @version 1.0 OperationAdminController
 */
package com.frt.generalgw.controller.operationadmin;

import com.frt.generalgw.client.usercore.operationadmin.OperationAdminClient;
import com.frt.generalgw.domain.param.operationadmin.auth.*;
import com.frt.generalgw.domain.param.operationadmin.forgotpassword.CheckSmsCodeParam;
import com.frt.generalgw.domain.param.operationadmin.forgotpassword.CheckVerifyCodeParam;
import com.frt.generalgw.domain.param.operationadmin.forgotpassword.GetVerifyCodeParam;
import com.frt.generalgw.domain.param.operationadmin.forgotpassword.SendSmsParam;
import com.frt.generalgw.domain.param.operationadmin.forgotpassword.UpdatePasswordParam;
import com.frt.generalgw.domain.result.CommonResult;
import com.frt.generalgw.domain.result.operationadmin.auth.OperationAdminLoginResult;
import com.frt.generalgw.domain.result.operationadmin.auth.OperationAdminResourceResult;
import com.frt.generalgw.domain.result.operationadmin.forgotpassword.CheckVerifyCodeResult;
import com.frt.generalgw.domain.result.operationadmin.forgotpassword.GetVerifyCodeResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 运营后台/OperationAdminAuthController
 *
 * <AUTHOR>
 * @version OperationAdminController.java, v 0.1 2025-08-27 14:45 tuyuwei
 */
@RestController
@RequestMapping("/operation/web")
public class OperationAdminAuthController {

    @Autowired
    private OperationAdminClient operationAdminClient;

    /**
     * 4.1 登录页资源获取接口
     * 接口名称：operation/web/search/resource
     * 请求方式：POST
     *
     * @param param 请求参数 webAddress 二级域名
     * @return 资源信息
     */
    @PostMapping("/search/resource")
    public OperationAdminResourceResult searchResource(@RequestBody OperationAdminResourceParam param) {
        return operationAdminClient.searchResource(param);
    }

    /**
     * 4.2 发送验证码
     * 接口名称：operation/web/send/code
     * 请求方式：POST
     *
     * @param param 请求参数
     *              tenantId 租户id
     *              phone 手机号
     *              type 场景类型 1-登录 2-修改密码
     * @return 发送结果
     */
    @PostMapping("/send/code")
    public CommonResult<Void> sendCode(@RequestBody OperationAdminSendCodeParam param) {
        operationAdminClient.sendCode(param);
        return CommonResult.success();
    }

    /**
     * 4.3 账号登录
     * 接口名称：operation/web/login
     * 请求方式：POST
     *
     * @param param 登录参数
     *              webAddress 二级域名
     *              tenantId 租户Id
     *              type 登录方式 1-密码登录 2-验证码登录
     *              account 账号
     *              password 密码（md5加密）
     *              code 验证码
     * @return 登录结果
     */
    @PostMapping("/login")
    public OperationAdminLoginResult login(@RequestBody OperationAdminLoginParam param) {
        return operationAdminClient.login(param);
    }

    /**
     * 4.7 账号登出
     * 接口名称：operation/web/logout
     * 请求方式：POST
     *
     * @return 登出结果
     */
    @PostMapping("/logout")
    public CommonResult<Void> logout() {
        operationAdminClient.logout();
        return CommonResult.success();
    }

    // ========== 忘记密码相关接口 ==========

    /**
     * 获取图形验证码
     *
     * @param param 请求参数
     * @return 验证码图片URL
     */
    @PostMapping("/get-verify-code")
    public GetVerifyCodeResult getVerifyCode(@Validated @RequestBody GetVerifyCodeParam param) {
        // TODO: 实现获取图形验证码逻辑
        return new GetVerifyCodeResult();
    }

    /**
     * 校验图文验证码
     *
     * @param param 请求参数
     * @return 校验结果
     */
    @PostMapping("/check-verify-code")
    public CheckVerifyCodeResult checkVerifyCode(@Validated @RequestBody CheckVerifyCodeParam param) {
        // TODO: 实现校验图文验证码逻辑
        return new CheckVerifyCodeResult();
    }

    /**
     * 发送短信
     *
     * @param param 请求参数
     */
    @PostMapping("/sen-sms")
    public void sendSms(@Validated @RequestBody SendSmsParam param) {
        // TODO: 实现发送短信逻辑
    }

    /**
     * 校验验证码
     *
     * @param param 请求参数
     */
    @PostMapping("/check-sms-code")
    public void checkSmsCode(@Validated @RequestBody CheckSmsCodeParam param) {
        // TODO: 实现校验短信验证码逻辑
    }

    /**
     * 修改密码
     *
     * @param param 请求参数
     */
    @PostMapping("/update-password")
    public void updatePassword(@Validated @RequestBody UpdatePasswordParam param) {
        // TODO: 实现修改密码逻辑
    }
}