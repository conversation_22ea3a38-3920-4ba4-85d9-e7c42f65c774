/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.generalgw.controller.operationadmin;

import com.frt.generalgw.client.usercore.protocol.ProtocolClient;
import com.frt.generalgw.domain.param.ProtocolListQueryParam;
import com.frt.generalgw.domain.result.ProtocolListQueryResult;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 商家版后台/ProtocolController
 *
 * <AUTHOR>
 * @version ProtocolController.java, v 0.1 2025-08-27 13:42 zhangling
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/operation/web")
public class ProtocolController {

    private final ProtocolClient protocolClient;

    /**
     * 获取协议列表
     *
     * @param param 请求参数
     * @return 协议列表
     */
    @PostMapping("/protocol-list")
    public ProtocolListQueryResult getProtocolList(@RequestBody ProtocolListQueryParam param) {
        // 实际实现中，这里应该调用服务层获取协议列表数据
        // 目前仅返回一个空的结果对象
        param.setTerminalType(1);
        return null;
    }
}