/**
 * fshows.com
 * Copyright (C) 2013-2025 All Rights Reserved.
 */
package com.frt.generalgw.controller.merchantmina;

import com.frt.generalgw.client.usercore.merchantadmin.StoreManagerClient;
import com.frt.generalgw.domain.param.common.PageParam;
import com.frt.generalgw.domain.param.merchantadmin.storemanager.StoreInfoAddParam;
import com.frt.generalgw.domain.param.merchantadmin.storemanager.StoreInfoQueryParam;
import com.frt.generalgw.domain.param.merchantadmin.storemanager.StoreInfoUpdateParam;
import com.frt.generalgw.domain.param.merchantadmin.storemanager.StoreListQueryParam;
import com.frt.generalgw.domain.result.common.PageResult;
import com.frt.generalgw.domain.result.merchantadmin.storemanager.StoreInfoAddResult;
import com.frt.generalgw.domain.result.merchantadmin.storemanager.StoreInfoQueryResult;
import com.frt.generalgw.domain.result.merchantadmin.storemanager.StoreInfoUpdateResult;
import com.frt.generalgw.domain.result.merchantadmin.storemanager.StoreListQueryResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 商家版后台/StoreManagerController
 */
@RestController
@RequestMapping("/merchant/web/store")
public class MerchantWebStoreManagerController {

    @Autowired
    private StoreManagerClient storeManagerClient;

    /**
     * 查询门店列表
     *
     * @param param
     * @return
     */
    @PostMapping("/query/list")
    public PageResult<StoreListQueryResult> queryStoreList(@RequestBody PageParam<StoreListQueryParam> param) {
        return storeManagerClient.queryStoreList(param);
    }

    /**
     * 新增门店
     *
     * @param param
     * @return
     */
    @PostMapping("/add/info")
    public StoreInfoAddResult addStoreInfo(@RequestBody StoreInfoAddParam param) {
        return storeManagerClient.addStoreInfo(param);
    }

    /**
     * 修改门店
     *
     * @param param
     * @return
     */
    @PostMapping("/update/info")
    public StoreInfoUpdateResult updateStoreInfo(@RequestBody StoreInfoUpdateParam param) {
        return storeManagerClient.updateStoreInfo(param);
    }

    /**
     * 门店详情
     *
     * @param param
     * @return
     */
    @PostMapping("/query/info")
    public StoreInfoQueryResult queryStoreInfo(@RequestBody StoreInfoQueryParam param) {
        return storeManagerClient.queryStoreInfo(param);
    }
}