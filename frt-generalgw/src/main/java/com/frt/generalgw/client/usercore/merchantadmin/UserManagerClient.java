/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.generalgw.client.usercore.merchantadmin;

import com.frt.generalgw.config.FeignConfig;
import com.frt.generalgw.domain.entity.UserInfo;
import com.frt.generalgw.domain.param.*;
import com.frt.generalgw.domain.param.common.PageParam;
import com.frt.generalgw.domain.result.CommonResult;
import com.frt.generalgw.domain.result.UserDetailQueryResult;
import com.frt.generalgw.domain.result.UserListQueryResult;
import com.frt.generalgw.domain.result.common.PageResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 员工管理客户端
 *
 * <AUTHOR>
 * @version UserManagerClient.java, v 0.1 2025-08-27 16:51 zhangling
 */
@FeignClient(
        contextId = "merchant-admin-user-manager-client",
        value = "frt-usercore-dev",
        configuration = {FeignConfig.class}
)
public interface UserManagerClient {

    /**
     * 查询员工列表
     *
     * @param param 请求参数
     * @return 员工列表
     */
    @PostMapping("/api/user/query-user-list")
    PageResult<UserInfo> getUserList(@RequestBody PageParam<UserListQueryParam> param);

    /**
     * 查询员工详情
     *
     * @param param 请求参数
     * @return 员工详情
     */
    @PostMapping("/api/user/get-user-detail")
    UserDetailQueryResult getUserDetail(@RequestBody UserDetailQueryParam param);

    /**
     * 添加员工
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @PostMapping("/api/user/add-user")
    CommonResult addUser(@RequestBody UserAddParam param);

    /**
     * 更新员工
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @PostMapping("/api/user/update-user")
    CommonResult<Void> updateUser(@RequestBody UserUpdateParam param);

    /**
     * 删除员工
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @PostMapping("/api/user/delete-user")
    CommonResult<Void> deleteUser(@RequestBody UserDeleteParam param);
}