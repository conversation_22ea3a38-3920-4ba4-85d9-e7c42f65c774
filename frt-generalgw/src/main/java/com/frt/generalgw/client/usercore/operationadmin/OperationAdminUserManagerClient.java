package com.frt.generalgw.client.usercore.operationadmin;

import com.frt.generalgw.domain.param.common.PageParam;
import com.frt.generalgw.domain.param.operationadmin.usermanager.UserAddParam;
import com.frt.generalgw.domain.param.operationadmin.usermanager.UserDetailQueryParam;
import com.frt.generalgw.domain.param.operationadmin.usermanager.UserListQueryParam;
import com.frt.generalgw.domain.param.operationadmin.usermanager.UserModifyParam;
import com.frt.generalgw.domain.result.common.PageResult;
import com.frt.generalgw.domain.result.operationadmin.usermanager.UserDetailQueryResult;
import com.frt.generalgw.domain.result.operationadmin.usermanager.UserListQueryResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 运营后台用户管理客户端
 *
 * <AUTHOR>
 * @version OperationAdminUserManagerClient.java, v 0.1 2025-08-28 15:00 tuyuwei
 */
@FeignClient(name = "usercore", contextId = "operationAdminUserManagerClient")
public interface OperationAdminUserManagerClient {

    /**
     * 员工列表
     *
     * @param param 请求参数
     * @return 员工列表
     */
    @PostMapping("/operation/admin/user/user-list")
    PageResult<UserListQueryResult> getUserList(@RequestBody PageParam<UserListQueryParam> param);

    /**
     * 新增员工
     *
     * @param param 请求参数
     */
    @PostMapping("/operation/admin/user/user-add")
    void addUser(@RequestBody UserAddParam param);

    /**
     * 修改员工
     *
     * @param param 请求参数
     */
    @PostMapping("/operation/admin/user/user-modify")
    void modifyUser(@RequestBody UserModifyParam param);

    /**
     * 查询员工信息
     *
     * @param param 请求参数
     * @return 员工信息
     */
    @PostMapping("/operation/admin/user/user-detail")
    UserDetailQueryResult getUserDetail(@RequestBody UserDetailQueryParam param);
}
