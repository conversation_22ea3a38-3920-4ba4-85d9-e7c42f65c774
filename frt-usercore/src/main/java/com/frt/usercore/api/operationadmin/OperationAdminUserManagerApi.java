package com.frt.usercore.api.operationadmin;

import com.frt.usercore.domain.param.common.PageParam;
import com.frt.usercore.domain.param.operationadmin.usermanager.UserAddParam;
import com.frt.usercore.domain.param.operationadmin.usermanager.UserDetailQueryParam;
import com.frt.usercore.domain.param.operationadmin.usermanager.UserListQueryParam;
import com.frt.usercore.domain.param.operationadmin.usermanager.UserModifyParam;
import com.frt.usercore.domain.result.common.PageResult;
import com.frt.usercore.domain.result.operationadmin.usermanager.UserDetailQueryResult;
import com.frt.usercore.domain.result.operationadmin.usermanager.UserListQueryResult;
import com.frt.usercore.service.OperationAdminUserManagerService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 运营后台用户管理API
 *
 * <AUTHOR>
 * @version OperationAdminUserManagerApi.java, v 0.1 2025-08-28 15:00 tuyuwei
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/operation/admin/user")
public class OperationAdminUserManagerApi {

    private final OperationAdminUserManagerService operationAdminUserManagerService;

    /**
     * 员工列表
     *
     * @param param 请求参数
     * @return 员工列表
     */
    @PostMapping("/user-list")
    public PageResult<UserListQueryResult> getUserList(@RequestBody PageParam<UserListQueryParam> param) {
        return operationAdminUserManagerService.getUserList(param);
    }

    /**
     * 新增员工
     *
     * @param param 请求参数
     */
    @PostMapping("/user-add")
    public void addUser(@RequestBody UserAddParam param) {
        operationAdminUserManagerService.addUser(param);
    }

    /**
     * 修改员工
     *
     * @param param 请求参数
     */
    @PostMapping("/user-modify")
    public void modifyUser(@RequestBody UserModifyParam param) {
        operationAdminUserManagerService.modifyUser(param);
    }

    /**
     * 查询员工信息
     *
     * @param param 请求参数
     * @return 员工信息
     */
    @PostMapping("/user-detail")
    public UserDetailQueryResult getUserDetail(@RequestBody UserDetailQueryParam param) {
        return operationAdminUserManagerService.getUserDetail(param);
    }
}
