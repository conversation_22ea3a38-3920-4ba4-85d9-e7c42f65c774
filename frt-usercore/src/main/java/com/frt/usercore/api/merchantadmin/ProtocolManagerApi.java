/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.usercore.api.merchantadmin;

import com.frt.usercore.domain.param.ProtocolInfoParam;
import com.frt.usercore.domain.param.ProtocolListQueryParam;
import com.frt.usercore.domain.result.ListResult;
import com.frt.usercore.domain.result.protocol.ProtocolInfoResult;
import com.frt.usercore.service.ProtocolService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version ProtocolManagerApi.java, v 0.1 2025-08-28 10:06 zhangling
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/protocol")
public class ProtocolManagerApi {

    private final ProtocolService protocolService;

    /**
     * 查询协议签署列表
     *
     * @return
     */
    @GetMapping("/query/protocol-list")
    ListResult<ProtocolInfoResult> findProtocolList(ProtocolListQueryParam param) {
        return protocolService.findProtocolList(param);
    }

    /**
     * 查询协议签署详情
     *
     * @return
     */
    @GetMapping("/get/protocol-info")
    ProtocolInfoResult getProtocolInfo(ProtocolInfoParam param) {
        return protocolService.getProtocolInfo(param);
    }
}