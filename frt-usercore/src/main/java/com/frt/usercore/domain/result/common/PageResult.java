package com.frt.usercore.domain.result.common;

import lombok.Data;

import java.util.List;

/**
 * 分页结果
 *
 * <AUTHOR>
 * @version PageResult.java, v 0.1 2025-08-28 15:00 tuyuwei
 */
@Data
public class PageResult<T> {

    /**
     * 总记录数
     */
    private Long total;

    /**
     * 当前页码
     */
    private Integer pageNum;

    /**
     * 每页大小
     */
    private Integer pageSize;

    /**
     * 总页数
     */
    private Integer pages;

    /**
     * 数据列表
     */
    private List<T> list;

    public PageResult() {
    }

    public PageResult(List<T> list) {
        this.list = list;
    }
}
