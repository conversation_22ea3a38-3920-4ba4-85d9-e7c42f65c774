package com.frt.usercore.domain.result.operationadmin.rolemanager;

import lombok.Data;

import java.util.List;

/**
 * 角色详情查询结果
 *
 * <AUTHOR>
 * @version RoleDetailQueryResult.java, v 0.1 2025-08-28 15:00 tuyuwei
 */
@Data
public class RoleDetailQueryResult {

    /**
     * 角色ID
     */
    private String roleId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色类型 0-超级管理员 1-普通角色
     */
    private Integer roleType;

    /**
     * 终端类型 1-商户端 2-运营端
     */
    private Integer terminalType;

    /**
     * 权限列表
     */
    private List<String> permissionValueList;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;
}
