/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.usercore.domain.param;

import lombok.Data;

import java.io.Serializable;

/**
 * 员工添加参数
 *
 * <AUTHOR>
 * @version UserAddParam.java, v 0.1 2025-08-27 16:51 zhangling
 */
@Data
public class UserAddParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 员工账号
     */
    private String account;

    /**
     * 员工姓名
     */
    private String name;

    /**
     * 员工手机号
     */
    private String phone;

    /**
     * 员工邮箱
     */
    private String email;

    /**
     * 员工密码
     */
    private String password;

    /**
     * 员工状态：Y-启用，N-禁用
     */
    private String status;

    /**
     * 角色ID
     */
    private String roleId;
}