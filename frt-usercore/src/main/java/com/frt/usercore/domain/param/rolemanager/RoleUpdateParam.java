package com.frt.usercore.domain.param.rolemanager;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 角色更新参数
 *
 * <AUTHOR>
 * @version RoleUpdateParam.java, v 0.1 2025-08-27 14:52 zhangling
 */
@Data
public class RoleUpdateParam implements Serializable {

    private static final long serialVersionUID = 5311686757375806690L;
    /**
     * 角色ID
     */
    private String roleId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 平台类型 1-商户后台 2-商户小程序
     */
    private Integer platformType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 权限列表
     */
    private List<String> permissionValueList;
}