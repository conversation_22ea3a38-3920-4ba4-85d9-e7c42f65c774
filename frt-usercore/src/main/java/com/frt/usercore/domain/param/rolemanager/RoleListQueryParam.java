package com.frt.usercore.domain.param.rolemanager;

import lombok.Data;

import java.io.Serializable;

/**
 * 角色列表查询参数
 *
 * <AUTHOR>
 * @version RoleListQueryParam.java, v 0.1 2025-08-27 14:52 zhangling
 */
@Data
public class RoleListQueryParam implements Serializable {

    private static final long serialVersionUID = 3065410004318016366L;
    /**
     * 页码
     */
    private Integer page;

    /**
     * 每页条数
     */
    private Integer pageSize;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色类型 1-收银员 2-店长
     */
    private Integer roleType;
}