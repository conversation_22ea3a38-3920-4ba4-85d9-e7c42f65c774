package com.frt.usercore.domain.param.operationadmin.usermanager;

import lombok.Getter;
import lombok.Setter;

/**
 * 新增员工参数
 *
 * <AUTHOR>
 * @version UserAddParam.java, v 0.1 2025-08-28 15:00 tuyuwei
 */
@Getter
@Setter
public class UserAddParam {

    /**
     * 访问令牌
     */
    private String accessToken;

    /**
     * 员工账号
     */
    private String username;

    /**
     * 密码（MD5）
     */
    private String password;

    /**
     * 员工姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 角色id
     */
    private String roleId;

    /**
     * 状态 1-正常 2-禁用 3-注销
     */
    private Integer accountStatus;
}
