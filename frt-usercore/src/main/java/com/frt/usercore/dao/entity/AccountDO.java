package com.frt.usercore.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * 运营后台端用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
@Data
@TableName("frt_account")
public class AccountDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 账号id
     */
    @TableField("user_id")
    private String userId;

    /**
     * 账号
     */
    @TableField("account")
    private String account;

    /**
     * 密码
     */
    @TableField("password")
    private String password;

    /**
     * 密码盐
     */
    @TableField("salt")
    private String salt;

    /**
     * 状态:1正常,2禁用,3注销
     */
    @TableField("account_status")
    private Integer accountStatus;

    /**
     * 平台类型 1-租户 2-代理商 3-商户
     */
    @TableField("platform_type")
    private Integer platformType;

    /**
     * 平台id(租户/代理商/商户id)
     */
    @TableField("platform_id")
    private String platformId;

    /**
     * 是否是管理员,0不是.1是
     */
    @TableField("is_admin")
    private Integer isAdmin;

    /**
     * 是否是重置了密码,0不是,1是
     */
    @TableField("is_reset_pwd")
    private Integer isResetPwd;

    /**
     * 手机号码
     */
    @TableField("phone")
    private String phone;

    /**
     * 姓名
     */
    @TableField("name")
    private String name;

    /**
     * 最后一次登录时间
     */
    @TableField("last_login_time")
    private Integer lastLoginTime;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 逻辑删除标志(0:未删除, 1:已删除)
     */
    @TableField("is_del")
    @TableLogic
    private Integer isDel;

    public Date getCreateTime() {
        if (this.createTime != null) {
          return new Date(this.createTime.getTime());
        } else {
          return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }
    public Date getUpdateTime() {
        if (this.updateTime != null) {
          return new Date(this.updateTime.getTime());
        } else {
          return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }


    public static final String ID = "id";

    public static final String ACCOUNT_ID = "account_id";

    public static final String ACCOUNT = "account";

    public static final String PASSWORD = "password";

    public static final String SALT = "salt";

    public static final String ACCOUNT_STATUS = "account_status";

    public static final String PLATFORM_TYPE = "platform_type";

    public static final String PLATFORM_ID = "platform_id";

    public static final String IS_ADMIN = "is_admin";

    public static final String IS_RESET_PWD = "is_reset_pwd";

    public static final String PHONE = "phone";

    public static final String NAME = "name";

    public static final String LAST_LOGIN_TIME = "last_login_time";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String IS_DEL = "is_del";

}
