package com.frt.usercore.dao.repository.impl;

import com.frt.usercore.dao.entity.AccountBindRoleDO;
import com.frt.usercore.dao.mapper.AccountBindRoleMapper;
import com.frt.usercore.dao.repository.AccountBindRoleDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 账号角色关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
@Service
public class AccountBindRoleDAOImpl extends ServiceImpl<AccountBindRoleMapper, AccountBindRoleDO> implements AccountBindRoleDAO {

}
