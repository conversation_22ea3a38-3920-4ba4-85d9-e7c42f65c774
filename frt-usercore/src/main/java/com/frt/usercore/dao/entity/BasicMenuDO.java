package com.frt.usercore.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * 导航菜单表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-27
 */
@Data
@TableName("frt_basic_menu")
public class BasicMenuDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 菜单ID
     */
    @TableField("menu_id")
    private String menuId;

    /**
     * 父菜单ID(0:根菜单)
     */
    @TableField("parent_menu_id")
    private String parentMenuId;

    /**
     * 菜单类型(1:页面,2:功能)
     */
    @TableField("menu_type")
    private Integer menuType;

    /**
     * 产品ID
     */
    @TableField("product_id")
    private String productId;

    /**
     * 菜单编码
     */
    @TableField("menu_code")
    private String menuCode;

    /**
     * 菜单名称
     */
    @TableField("menu_name")
    private String menuName;

    /**
     * 菜单平台(1:后台,2:小程序)
     */
    @TableField("menu_platform")
    private Integer menuPlatform;

    /**
     * 菜单描述
     */
    @TableField("menu_description")
    private String menuDescription;

    /**
     * 接口路径
     */
    @TableField("api_path")
    private String apiPath;

    /**
     * 接口描述
     */
    @TableField("api_description")
    private String apiDescription;

    /**
     * 是否启用(0:禁用,1:启用)
     */
    @TableField("is_enabled")
    private Integer isEnabled;

    /**
     * 是否可见(0:隐藏,1:显示)
     */
    @TableField("is_visible")
    private Integer isVisible;

    /**
     * 是否权限校验(0:否,1:是)
     */
    @TableField("is_permission")
    private Integer isPermission;

    /**
     * 逻辑删除标志(0:未删除, 1:已删除)
     */
    @TableField("is_del")
    @TableLogic
    private Integer isDel;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 更新人
     */
    @TableField("updated_by")
    private String updatedBy;

    public Date getCreateTime() {
        if (this.createTime != null) {
          return new Date(this.createTime.getTime());
        } else {
          return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }
    public Date getUpdateTime() {
        if (this.updateTime != null) {
          return new Date(this.updateTime.getTime());
        } else {
          return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }


    public static final String ID = "id";

    public static final String MENU_ID = "menu_id";

    public static final String PARENT_MENU_ID = "parent_menu_id";

    public static final String MENU_TYPE = "menu_type";

    public static final String PRODUCT_ID = "product_id";

    public static final String MENU_CODE = "menu_code";

    public static final String MENU_NAME = "menu_name";

    public static final String MENU_PLATFORM = "menu_platform";

    public static final String MENU_DESCRIPTION = "menu_description";

    public static final String API_PATH = "api_path";

    public static final String API_DESCRIPTION = "api_description";

    public static final String IS_ENABLED = "is_enabled";

    public static final String IS_VISIBLE = "is_visible";

    public static final String IS_PERMISSION = "is_permission";

    public static final String IS_DEL = "is_del";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String CREATED_BY = "created_by";

    public static final String UPDATED_BY = "updated_by";

}
