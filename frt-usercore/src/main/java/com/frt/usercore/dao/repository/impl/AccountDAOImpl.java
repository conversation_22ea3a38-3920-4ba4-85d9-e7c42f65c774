package com.frt.usercore.dao.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.frt.usercore.dao.entity.AccountDO;
import com.frt.usercore.dao.mapper.AccountMapper;
import com.frt.usercore.dao.repository.AccountDAO;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 运营后台端用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
@Service
public class AccountDAOImpl extends ServiceImpl<AccountMapper, AccountDO> implements AccountDAO {

    @Override
    public AccountDO getByAccountAndPasswordAndPlatformId(String account, String password, Integer platformType) {
        LambdaQueryWrapper<AccountDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AccountDO::getAccount, account)
                   .eq(AccountDO::getPassword, password)
                   .eq(AccountDO::getPlatformType, platformType);
        return this.getOne(queryWrapper);
    }
}
