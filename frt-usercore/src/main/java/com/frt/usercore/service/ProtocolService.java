/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.usercore.service;

import com.frt.usercore.domain.param.ProtocolInfoParam;
import com.frt.usercore.domain.param.ProtocolListQueryParam;
import com.frt.usercore.domain.result.ListResult;
import com.frt.usercore.domain.result.protocol.ProtocolInfoResult;

/**
 * <AUTHOR>
 * @version ProtocolService.java, v 0.1 2025-08-28 10:08 zhangling
 */
public interface ProtocolService {

    /**
     * 查询协议列表
     * @param param
     * @return
     */
    ListResult<ProtocolInfoResult> findProtocolList(ProtocolListQueryParam param);

    /**
     * 查询协议内容
     * @param param
     * @return
     */
    ProtocolInfoResult getProtocolInfo(ProtocolInfoParam param);
}