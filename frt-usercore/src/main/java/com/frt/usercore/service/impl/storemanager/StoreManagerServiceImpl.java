package com.frt.usercore.service.impl.storemanager;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.frt.usercore.dao.entity.MerchantStoreInfoDO;
import com.frt.usercore.dao.repository.MerchantStoreInfoDAO;
import com.frt.usercore.domain.mapper.StoreManagerMapper;
import com.frt.usercore.domain.param.PageParam;
import com.frt.usercore.domain.param.storemanager.StoreInfoAddParam;
import com.frt.usercore.domain.param.storemanager.StoreInfoQueryParam;
import com.frt.usercore.domain.param.storemanager.StoreInfoUpdateParam;
import com.frt.usercore.domain.param.storemanager.StoreListQueryParam;
import com.frt.usercore.domain.result.PageResult;
import com.frt.usercore.domain.result.storemanager.StoreInfoAddResult;
import com.frt.usercore.domain.result.storemanager.StoreInfoQueryResult;
import com.frt.usercore.domain.result.storemanager.StoreInfoUpdateResult;
import com.frt.usercore.domain.result.storemanager.StoreListQueryResult;
import com.frt.usercore.service.storemanager.StoreManagerService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class StoreManagerServiceImpl implements StoreManagerService {

    @Autowired
    private MerchantStoreInfoDAO merchantStoreInfoDAO;

    @Autowired
    private StoreManagerMapper storeManagerMapper;

    @Override
    public PageResult<StoreListQueryResult> queryStoreList(PageParam<StoreListQueryParam> param) {
        // todo xujw 缺少登录信息
        Page<MerchantStoreInfoDO> pageDO = merchantStoreInfoDAO.lambdaQuery()
                .eq(StrUtil.isNotBlank(param.getQuery().getStoreId()), MerchantStoreInfoDO::getStoreId, param.getQuery().getStoreId())
                .eq(StrUtil.isNotBlank(param.getQuery().getStoreName()), MerchantStoreInfoDO::getStoreName, param.getQuery().getStoreName())
                .eq(StrUtil.isNotBlank(param.getQuery().getIsShow()), MerchantStoreInfoDO::getIsShow, param.getQuery().getIsShow())
                .orderByAsc(MerchantStoreInfoDO::getCreateTime)
                .page(Page.of(param.getPage(), param.getPageSize()));
        if (CollectionUtil.isEmpty(pageDO.getRecords())) {
            PageResult<StoreListQueryResult> pageResult = new PageResult<>();
            pageResult.setTotal(pageDO.getTotal());
            pageResult.setSize(pageDO.getSize());
            pageResult.setCurrent(pageDO.getCurrent());
            pageResult.setRecords(Lists.newArrayList());
            return pageResult;
        }

        List<StoreListQueryResult> resultList = new ArrayList<>();
        List<MerchantStoreInfoDO> records = pageDO.getRecords();
        for (MerchantStoreInfoDO item : records) {
            StoreListQueryResult result = storeManagerMapper.coverMerchantStoreInfoDOToStoreListQueryResult(item);
            resultList.add(result);
        }
        PageResult<StoreListQueryResult> pageResult = new PageResult<>();
        pageResult.setTotal(pageDO.getTotal());
        pageResult.setSize(pageDO.getSize());
        pageResult.setCurrent(pageDO.getCurrent());
        pageResult.setRecords(resultList);
        return pageResult;
    }

    @Override
    public StoreInfoAddResult addStoreInfo(StoreInfoAddParam param) {
        // todo xujw 缺少登录信息
        MerchantStoreInfoDO storeInfoDO = storeManagerMapper.coverStoreInfoAddParamToStoreInfoDO(param);
        merchantStoreInfoDAO.save(storeInfoDO);
        StoreInfoAddResult result = new StoreInfoAddResult();
        result.setSuccess(true);
        return result;
    }

    @Override
    public StoreInfoUpdateResult updateStoreInfo(StoreInfoUpdateParam param) {
        // todo xujw 缺少登录信息
        MerchantStoreInfoDO storeInfoDO = storeManagerMapper.coverStoreInfoUpdateParamToStoreInfoDO(param);
        merchantStoreInfoDAO.updateById(storeInfoDO);
        StoreInfoUpdateResult result = new StoreInfoUpdateResult();
        result.setSuccess(true);
        return result;
    }

    @Override
    public StoreInfoQueryResult queryStoreInfo(StoreInfoQueryParam param) {
        MerchantStoreInfoDO infoDO = merchantStoreInfoDAO.lambdaQuery()
                .eq(MerchantStoreInfoDO::getStoreId, param.getStoreId())
                .one();
        if (infoDO == null) {
            StoreInfoQueryResult result = new StoreInfoQueryResult();
            return result;
        }

        StoreInfoQueryResult result = storeManagerMapper.coverStoreInfoUpdateParamToStoreInfoQueryResult(infoDO);
        return result;
    }
}
