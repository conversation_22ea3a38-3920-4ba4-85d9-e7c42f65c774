/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.usercore.service;

import com.frt.usercore.domain.param.rolemanager.RoleAddParam;
import com.frt.usercore.domain.param.rolemanager.RoleDeleteParam;
import com.frt.usercore.domain.param.rolemanager.RoleDetailQueryParam;
import com.frt.usercore.domain.param.rolemanager.RoleListQueryParam;
import com.frt.usercore.domain.param.rolemanager.RoleUpdateParam;
import com.frt.usercore.domain.result.common.CommonResult;
import com.frt.usercore.domain.result.rolemanager.RoleDetailQueryResult;
import com.frt.usercore.domain.result.rolemanager.RoleListQueryResult;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version RoleManagerService.java, v 0.1 2025-08-27 16:34 zhangling
 */
public interface RoleManagerService {
    /**
     * 查询角色列表
     *
     * @param param 请求参数
     * @return 角色列表
     */
    RoleListQueryResult getRoleList(@RequestBody RoleListQueryParam param);

    /**
     * 查询角色详情
     *
     * @param param 请求参数
     * @return 角色详情
     */
    RoleDetailQueryResult getRoleDetail(@RequestBody RoleDetailQueryParam param);

    /**
     * 添加角色
     *
     * @param param 请求参数
     * @return 操作结果
     */
    CommonResult addRole(@RequestBody RoleAddParam param);

    /**
     * 更新角色
     *
     * @param param 请求参数
     * @return 操作结果
     */
    CommonResult updateRole(@RequestBody RoleUpdateParam param);

    /**
     * 删除角色
     *
     * @param param 请求参数
     * @return 操作结果
     */
    CommonResult deleteRole(@RequestBody RoleDeleteParam param);
}