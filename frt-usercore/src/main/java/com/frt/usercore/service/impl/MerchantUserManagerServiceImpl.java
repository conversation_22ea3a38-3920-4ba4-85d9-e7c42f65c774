/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.usercore.service.impl;

import com.frt.usercore.dao.repository.MerchantDAO;
import com.frt.usercore.dao.repository.MerchantUserDAO;
import com.frt.usercore.domain.entity.UserInfo;
import com.frt.usercore.domain.param.*;
import com.frt.usercore.domain.result.PageResult;
import com.frt.usercore.domain.result.UserDetailQueryResult;
import com.frt.usercore.domain.result.common.CommonResult;
import com.frt.usercore.service.MerchantUserManagerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version MerchantUserManagerServiceImpl.java, v 0.1 2025-08-27 17:58 zhangling
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MerchantUserManagerServiceImpl implements MerchantUserManagerService {

    private final MerchantUserDAO merchantUserDAO;

    private final MerchantDAO merchantDAO;

    /**
     * 查询员工列表
     *
     * @param param 请求参数
     * @return 员工列表
     */
    @Override
    public PageResult<UserInfo> getUserList(PageParam<UserListQueryParam> param) {
        return null;
    }

    /**
     * 查询员工详情
     *
     * @param param 请求参数
     * @return 员工详情
     */
    @Override
    public UserDetailQueryResult getUserDetail(UserDetailQueryParam param) {
        return null;
    }

    /**
     * 添加员工
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @Override
    public CommonResult addUser(UserAddParam param) {
        return null;
    }

    /**
     * 更新员工
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @Override
    public CommonResult updateUser(UserUpdateParam param) {
        return null;
    }

    /**
     * 删除员工
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @Override
    public CommonResult deleteUser(UserDeleteParam param) {
        return null;
    }

    /**
     * 禁用员工
     *
     * @param param 禁用员工参数
     * @return 操作结果
     */
    @Override
    public CommonResult disableAndEnableUser(UserDisableAndEnableParam param) {
        return null;
    }
}