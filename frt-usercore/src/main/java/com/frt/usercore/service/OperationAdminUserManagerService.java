package com.frt.usercore.service;

import com.frt.usercore.domain.param.common.PageParam;
import com.frt.usercore.domain.param.operationadmin.usermanager.UserAddParam;
import com.frt.usercore.domain.param.operationadmin.usermanager.UserDetailQueryParam;
import com.frt.usercore.domain.param.operationadmin.usermanager.UserListQueryParam;
import com.frt.usercore.domain.param.operationadmin.usermanager.UserModifyParam;
import com.frt.usercore.domain.result.common.PageResult;
import com.frt.usercore.domain.result.operationadmin.usermanager.UserDetailQueryResult;
import com.frt.usercore.domain.result.operationadmin.usermanager.UserListQueryResult;

/**
 * 运营后台用户管理服务接口
 *
 * <AUTHOR>
 * @version OperationAdminUserManagerService.java, v 0.1 2025-08-28 15:00 tuyuwei
 */
public interface OperationAdminUserManagerService {

    /**
     * 员工列表
     *
     * @param param 请求参数
     * @return 员工列表
     */
    PageResult<UserListQueryResult> getUserList(PageParam<UserListQueryParam> param);

    /**
     * 新增员工
     *
     * @param param 请求参数
     */
    void addUser(UserAddParam param);

    /**
     * 修改员工
     *
     * @param param 请求参数
     */
    void modifyUser(UserModifyParam param);

    /**
     * 查询员工信息
     *
     * @param param 请求参数
     * @return 员工信息
     */
    UserDetailQueryResult getUserDetail(UserDetailQueryParam param);
}
