package com.frt.usercore.service;

import com.frt.usercore.domain.param.common.PageParam;
import com.frt.usercore.domain.param.operationadmin.rolemanager.RoleAddParam;
import com.frt.usercore.domain.param.operationadmin.rolemanager.RoleDetailQueryParam;
import com.frt.usercore.domain.param.operationadmin.rolemanager.RoleListQueryParam;
import com.frt.usercore.domain.param.operationadmin.rolemanager.RoleModifyParam;
import com.frt.usercore.domain.result.common.PageResult;
import com.frt.usercore.domain.result.operationadmin.rolemanager.RoleDetailQueryResult;
import com.frt.usercore.domain.result.operationadmin.rolemanager.RoleListQueryResult;

/**
 * 运营后台角色管理服务接口
 *
 * <AUTHOR>
 * @version OperationAdminRoleManagerService.java, v 0.1 2025-08-28 15:00 tuyuwei
 */
public interface OperationAdminRoleManagerService {

    /**
     * 角色列表
     *
     * @param param 请求参数
     * @return 角色列表
     */
    PageResult<RoleListQueryResult> getRoleList(PageParam<RoleListQueryParam> param);

    /**
     * 新增角色
     *
     * @param param 请求参数
     */
    void addRole(RoleAddParam param);

    /**
     * 修改角色
     *
     * @param param 请求参数
     */
    void modifyRole(RoleModifyParam param);

    /**
     * 角色详情
     *
     * @param param 请求参数
     * @return 角色详情
     */
    RoleDetailQueryResult getRoleDetail(RoleDetailQueryParam param);
}
