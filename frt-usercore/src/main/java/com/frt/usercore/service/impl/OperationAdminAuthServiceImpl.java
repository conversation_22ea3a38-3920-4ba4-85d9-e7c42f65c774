package com.frt.usercore.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.digest.MD5;
import com.frt.usercore.common.enums.business.AccountStatusEnum;
import com.frt.usercore.common.enums.business.PlatformEnum;
import com.frt.usercore.common.enums.exception.AuthErrorEnum;
import com.frt.usercore.dao.entity.AccountDO;
import com.frt.usercore.dao.repository.AccountDAO;
import com.frt.usercore.domain.result.operationadmin.auth.OperationAdminLoginResult;
import com.frt.usercore.domain.result.operationadmin.auth.OperationAdminResourceResult;
import com.frt.usercore.domain.param.operationadmin.auth.OperationAdminChangePasswordParam;
import com.frt.usercore.domain.param.operationadmin.auth.OperationAdminCheckCodeParam;
import com.frt.usercore.domain.param.operationadmin.auth.OperationAdminLoginParam;
import com.frt.usercore.domain.param.operationadmin.auth.OperationAdminResourceParam;
import com.frt.usercore.domain.param.operationadmin.auth.OperationAdminSearchPhoneParam;
import com.frt.usercore.domain.param.operationadmin.auth.OperationAdminSendCodeParam;
import com.frt.usercore.service.OperationAdminAuthService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;


import javax.annotation.Resource;
import java.util.List;

/**
 * 运营后台权限服务实现类
 */
@Slf4j
@Service
public class OperationAdminAuthServiceImpl implements OperationAdminAuthService {

    @Resource
    private AccountDAO accountDAO;
    
    // 是否允许多点登录
    @Value("${sa-token.is-concurrent: false}")
    private Boolean isConcurrent;
    
    // 同时登录设备最大数量
    @Value("${sa-token.max-login-count: 1}")
    private Integer maxLoginCount;
    
    // token过期时间(秒)
    @Value("${sa-token.timeout: 2592000}")
    private Long timeout;

    /**
     * 4.1 登录页资源获取接口
     * 接口名称：operation/web/search/resource
     * 请求方式：POST
     *
     * @param param 请求参数 webAddress 二级域名
     * @return 资源信息
     */
    @Override
    public OperationAdminResourceResult searchResource(OperationAdminResourceParam param) {
        // TODO: 实现具体业务逻辑
        OperationAdminResourceResult result = new OperationAdminResourceResult();
        // 示例数据，实际应从数据库或其他服务获取
        result.setTenantId("tenant-001");
        result.setTenantName("示例运营租户");
        result.setBrandLogo("https://example.com/logo.png");
        result.setBackgroundImage("https://example.com/background.jpg");
        result.setThemeColor("#4A90E2");
        result.setSendCode(true);
        return result;
    }

    /**
     * 4.2 发送验证码
     * 接口名称：operation/web/send/code
     * 请求方式：POST
     *
     * @param param 请求参数
     *              tenantId 租户id
     *              phone 手机号
     *              type 场景类型 1-登录 2-修改密码
     */
    @Override
    public void sendCode(OperationAdminSendCodeParam param) {
        // TODO: 实现具体业务逻辑
        // 示例：调用短信服务发送验证码
    }

    /**
     * 4.3 账号登录
     * 接口名称：operation/web/login
     * 请求方式：POST
     *
     * @param param 登录参数
     *              webAddress 二级域名
     *              tenantId 租户Id
     *              type 登录方式 1-密码登录 2-验证码登录
     *              account 账号
     *              password 密码（md5加密）
     *              code 验证码
     * @return 登录结果
     */
    @Override
    public OperationAdminLoginResult login(OperationAdminLoginParam param) {
        // TODO: 实现具体业务逻辑
        if (1 == param.getType()) {
            //密码登录
            if (StringUtils.isBlank(param.getAccount()) || StringUtils.isBlank(param.getPassword()) || StringUtils.isBlank(param.getTenantId())) {
                throw AuthErrorEnum.ACCOUNT_NOT_EXIST.exception();
            }
            AccountDO accountDO = accountDAO.selectByAccountAndPlatformType(param.getAccount(), PlatformEnum.OPERATION.getCode(), param.getTenantId());
            if (ObjectUtil.isNull(accountDO)) {
                throw AuthErrorEnum.ACCOUNT_NOT_EXIST.exception();
            }
            
            // 验证密码
            String encryptedPassword = MD5.create().digestHex(param.getPassword()+accountDO.getSalt());
            if (!encryptedPassword.equals(accountDO.getPassword())) {
                throw AuthErrorEnum.ACCOUNT_PASSWORD_ERROR.exception();
            }
            // 检查账号状态
            if (!accountDO.getAccountStatus().equals(AccountStatusEnum.NORMAL.getCode())) {
                throw AuthErrorEnum.ACCOUNT_NOT_ACTIVE.exception();
            }
            
            // 处理多点登录逻辑
            if (!isConcurrent) {
                // 不允许多点登录，踢掉已登录的设备
                StpUtil.logoutByLoginId(accountDO.getId());
            } else if (maxLoginCount > 0) {
                // 检查当前登录设备数量
                List<String> tokenList = StpUtil.getTokenValueListByLoginId(accountDO.getId());
                if (tokenList.size() >= maxLoginCount) {
                    // 超出最大登录设备数，踢掉最早登录的设备
                    StpUtil.logoutByTokenValue(tokenList.get(0));
                }
            }
            
            // 设置登录超时时间
            StpUtil.setTimeout(timeout);
            
            // 登录
            StpUtil.login(accountDO.getId());
            StpUtil.getTokenSession().set("accountInfo", accountDO);
        } else if (2 == param.getType()) {
            //验证码登录
        }
        // 示例数据，实际应进行用户验证并生成token
        OperationAdminLoginResult result = new OperationAdminLoginResult();
        result.setToken(StpUtil.getTokenValue());

        OperationAdminLoginResult.UserInfo userInfo = new OperationAdminLoginResult.UserInfo();
        AccountDO currentAccount = (AccountDO) StpUtil.getTokenSession().get("accountInfo");
        userInfo.setAccountId(String.valueOf(currentAccount.getId()));
        userInfo.setAccount(currentAccount.getAccount());
        userInfo.setIsAdmin(String.valueOf(currentAccount.getUserType()));
        userInfo.setName(currentAccount.getName());
        userInfo.setPhone(currentAccount.getPhone());
        result.setUserInfo(userInfo);

        OperationAdminLoginResult.TenantInfo tenantInfo = new OperationAdminLoginResult.TenantInfo();
        tenantInfo.setTenantId(currentAccount.getTenantId());
        tenantInfo.setTenantName(currentAccount.getTenantName());
        tenantInfo.setPhone(currentAccount.getPhone());
        result.setTenantInfo(tenantInfo);

        return result;
    }

    /**
     * 4.4 通过账号查询加密手机号
     * 接口名称：operation/web/search/phone
     * 请求方式：POST
     *
     * @param param 查询参数 account 账号
     * @return 手机号信息
     */
    @Override
    public String searchPhone(OperationAdminSearchPhoneParam param) {
        // TODO: 实现具体业务逻辑
        // 示例数据，实际应从数据库查询用户信息
        return "138****8888";
    }

    /**
     * 4.5 修改密码验证码校验
     * 接口名称：operation/web/check/code
     * 请求方式：POST
     *
     * @param param 验证参数
     *              code 验证码
     *              account 账号
     */
    @Override
    public void checkCode(OperationAdminCheckCodeParam param) {
        // TODO: 实现具体业务逻辑
        // 示例：校验验证码是否正确
    }

    /**
     * 4.6 设置新密码
     * 接口名称：operation/web/change/password
     * 请求方式：POST
     *
     * @param param 修改密码参数
     *              account 账号
     *              password 新密码
     *              secondPassword 新密码确认
     */
    @Override
    public void changePassword(OperationAdminChangePasswordParam param) {
        // TODO: 实现具体业务逻辑
        // 示例：更新用户密码
    }

    /**
     * 4.7 账号登出
     * 接口名称：operation/web/logout
     * 请求方式：POST
     */
    @Override
    public void logout() {
        // TODO: 实现具体业务逻辑
        // 示例：清理用户登录状态
        StpUtil.logout();
    }
}
