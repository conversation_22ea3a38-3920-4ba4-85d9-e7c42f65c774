/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.usercore.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.frt.usercore.common.enums.business.PlatformEnum;
import com.frt.usercore.dao.entity.AccountDO;
import com.frt.usercore.dao.entity.ProtocolConfigDO;
import com.frt.usercore.dao.entity.UserProtocolSignDO;
import com.frt.usercore.dao.repository.AccountDAO;
import com.frt.usercore.dao.repository.ProtocolConfigDAO;
import com.frt.usercore.dao.repository.UserProtocolSignDAO;
import com.frt.usercore.domain.mapper.ProtocolMapper;
import com.frt.usercore.domain.param.ProtocolInfoParam;
import com.frt.usercore.domain.param.ProtocolListQueryParam;
import com.frt.usercore.domain.result.ListResult;
import com.frt.usercore.domain.result.protocol.ProtocolInfoResult;
import com.frt.usercore.service.ProtocolService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version ProtocolServiceImpl.java, v 0.1 2025-08-28 10:08 zhangling
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProtocolServiceImpl implements ProtocolService {

    private final ProtocolConfigDAO protocolConfigDAO;

    private final UserProtocolSignDAO userProtocolSignDAO;

    private final AccountDAO accountDAO;

    private final ProtocolMapper protocolMapper;

    /**
     * 查询协议列表
     *
     * @param param
     * @return
     */
    @Override
    public ListResult<ProtocolInfoResult> findProtocolList(ProtocolListQueryParam param) {
        final AccountDO accountDO = accountDAO.getByAccountAndPasswordAndPlatformId(param.getAccount(), param.getPassword(), PlatformEnum.MERCHANT.getCode());
        if (ObjectUtil.isNull(accountDO)) {
            return new ListResult<>();
        }
        List<ProtocolConfigDO> protocolList = protocolConfigDAO.findByTerminalUserType(PlatformEnum.MERCHANT.getCode());
        // 已签署的协议列表
        final List<UserProtocolSignDO> signedList = userProtocolSignDAO.findByUserId(accountDO.getUserId());
        if (CollectionUtil.isNotEmpty(signedList)) {
            protocolList = protocolList.stream().filter(protocol -> signedList.stream().anyMatch(signed -> signed.getProtocolId().equals(protocol.getProtocolId()))).toList();
        }
        return new ListResult<>(protocolList.stream().map(protocolMapper::coverProtocolConfigDOToProtocolInfoResult).toList());
    }

    /**
     * 查询协议内容
     *
     * @param param
     * @return
     */
    @Override
    public ProtocolInfoResult getProtocolInfo(ProtocolInfoParam param) {
        final AccountDO accountDO = accountDAO.getByAccountAndPasswordAndPlatformId(param.getAccount(), param.getPassword(), PlatformEnum.MERCHANT.getCode());
        if (ObjectUtil.isNull(accountDO)) {
            throw new RuntimeException("账号或密码错误");
        }
        final ProtocolConfigDO protocolConfigDO = protocolConfigDAO.getById(param.getProtocolId());
        if (ObjectUtil.isNull(protocolConfigDO)) {
            throw new RuntimeException("协议不存在");
        }
        return protocolMapper.coverProtocolConfigDOToProtocolInfoResult(protocolConfigDO);
    }
}