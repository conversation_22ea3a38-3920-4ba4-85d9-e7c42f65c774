package com.frt.usercore.common.enums.business;

import lombok.Getter;

/**
 * 平台类型枚举
 */
@Getter
public enum PlatformEnum {

    /**
     * 运营后台
     */
    OPERATION(1, "运营后台"),

    /**
     * 代理商
     */
    AGENT(2, "代理商"),

    /**
     * 商户
     */
    MERCHANT(3, "商户");

    private final Integer code;
    private final String description;

    PlatformEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }
}